/// <summary>
/// 公用基础函数库
/// </summary>
using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;
using Random = UnityEngine.Random;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using System.Diagnostics;

namespace Utils
{

    public class stringUtils
    {
        public static Dictionary<string, string> ParserJsonToDictionary(string str)
        {
            //将json字符串解析为字典
            Dictionary<string, string> dic = new Dictionary<string, string>();
            string pattern = @"{(.*?)}"; //匹配{}中的内容
            Regex regex = new Regex(pattern);
            str = regex.Match(str).Groups[1].Value;

            string[] strs = str.Split('&');
            foreach (string s in strs)
            {
                string[] temp = s.Split(':');
                char[] remove_chars = { '“', '”', '"', ' ' }; 
                dic.Add(temp[0].Trim(remove_chars), temp[1].Trim(remove_chars));
            }
            return dic;
        }
        public static string SerializeDictionaryToJsonString<TKey, TValue>(Dictionary<TKey, TValue> dict)
        {
            if (dict.Count == 0)
                return "";

            string jsonStr = JsonConvert.SerializeObject(dict);
            return jsonStr;
        }

        /// <summary>
        /// 将json字符串反序列化为字典类型
        /// </summary>
        /// <typeparam name="TKey">字典key</typeparam>
        /// <typeparam name="TValue">字典value</typeparam>
        /// <param name="jsonStr">json字符串</param>
        /// <returns>字典数据</returns>
        public static Dictionary<TKey, TValue> DeserializeStringToDictionary<TKey, TValue>(string jsonStr)
        {
            if (string.IsNullOrEmpty(jsonStr))
                return new Dictionary<TKey, TValue>();

            Dictionary<TKey, TValue> jsonDict = JsonConvert.DeserializeObject<Dictionary<TKey, TValue>>(jsonStr);

            return jsonDict;

        }
    }
    public class LocationUtils
    {
        public static bool IsValidPosition2D(Vector2 pos, Transform confiner, LayerMask layer)
        {
            //pos为当前位置，confiner为地图边界，layer为地图层
            //判定当前位置是否在地图边界内且是否与在layer中的物体重叠
            bool isInsidePolygon = IsInsidePolygon(confiner, pos);
            if (isInsidePolygon)
            {
                //判定当前位置是否存在layer中的物体
                Collider2D[] colliders = Physics2D.OverlapPointAll(pos, layer);
                if (colliders.Length > 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                return false;
            }

        }
        public static bool IsInsidePolygon(Transform confiner, Vector2 p)
        {
            PolygonCollider2D collider = confiner.GetComponent<PolygonCollider2D>();
            Vector2[] vector = new Vector2[collider.GetTotalPointCount()];

            for (int i = 0; i < collider.GetTotalPointCount(); i++)
            {
                //本地坐标变为屏幕坐标
                vector[i] = new Vector2(confiner.position.x, confiner.position.y) + collider.points[i];
            }

            //判断点是否在多边形内
            return PositionPnpoly(vector, p);
        }

        public static bool PositionPnpoly(Vector2[] Overlaps, Vector2 p)
        {
            /// 判断点是否在多边形内.
            /// 注意到如果从P作水平向左的射线的话，如果P在多边形内部，那么这条射线与多边形的交点必为奇数，
            /// 如果P在多边形外部，则交点个数必为偶数(0也在内)。
            /// 所以，我们可以顺序考虑多边形的每条边，求出交点的总个数。还有一些特殊情况要考虑。假如考虑边(P1,P2)，
            /// 1)如果射线正好穿过P1或者P2,那么这个交点会被算作2次，处理办法是如果P的从坐标与P1,P2中较小的纵坐标相同，则直接忽略这种情况
            /// 2)如果射线水平，则射线要么与其无交点，要么有无数个，这种情况也直接忽略。
            /// 3)如果射线竖直，而P0的横坐标小于P1,P2的横坐标，则必然相交。
            /// 4)再判断相交之前，先判断P是否在边(P1,P2)的上面，如果在，则直接得出结论：P再多边形内部。
            /// </summary>
            /// <param name="Overlaps">不规则坐标集合</param>
            /// <param name="p">当前点击坐标</param>
            /// <returns></returns>
            int i, j, c = 0;
            for (i = 0, j = Overlaps.Length - 1; i < Overlaps.Length; j = i++)
            {

                if (((Overlaps[i].y > p.y) != (Overlaps[j].y > p.y)) && (p.x < (Overlaps[j].x - Overlaps[i].x) * (p.y - Overlaps[i].y) / (Overlaps[j].y - Overlaps[i].y) + Overlaps[i].x))
                {
                    // Debug.Log(i);
                    c = 1 + c; ;
                }
            }
            if (c % 2 == 0)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        public static Vector2 GetRandomPosition2D(Vector2 pos, Vector2 faceDirection, float distance, float radius)
        {
            //在faceDirection正前方的长度为distance角度为radius的扇形区域内生成一个随机位置
            //在-radius,radius范围内生成一个随机角度
            float angle = Random.Range(-radius, radius);
            //在0,distance范围内生成一个随机半径
            float r = Random.Range(0, distance);
            //基于faceDirection计算出一个新的方向
            Vector2 newDirection = Quaternion.Euler(0, 0, angle) * faceDirection;
            newDirection.Normalize();
            //计算出一个新的位置
            Vector2 newPosition = pos + newDirection * r;
            return newPosition;
        }
    }
    public class MathUtils
    {
        public static float skillHasteToCooldownReduction(float skillHaste)
        {
            //将技能加速转换为技能冷却缩减
            return 100 / (100 + skillHaste);
        }
    }

    
 
    public class StackTraceHelper
    {
        /// <summary>
        /// 获取调用者的方法名
        /// </summary>
        /// <returns>调用者的方法名</returns>
        public static string GetCallerMethodName()
        {
            StackTrace stackTrace = new StackTrace();
            StackFrame[] stackFrames = stackTrace.GetFrames();

            // 获取调用者的方法名
            string callerMethodName = string.Empty;
            if (stackFrames.Length > 2)
            {
                callerMethodName = stackFrames[2].GetMethod().Name;
            }

            return callerMethodName;
        }
    }
}

