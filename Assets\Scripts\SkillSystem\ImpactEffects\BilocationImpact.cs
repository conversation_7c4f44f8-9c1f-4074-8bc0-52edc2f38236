using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Utils;

public class BilocationImpact : IImpactEffect
{
    public IEnumerator Execute(SkillDeployer deployer)
    {
        //记录当前技能数据
        SkillData skillData = ScriptableObject.CreateInstance<SkillData>();
        skillData = deployer.SkillData;

        //技能前摇
        //暂停enmy的移动
        //获取敌人
        float _loopTime = 3f;
        Enemy enemy = deployer.owner.GetComponent<Enemy>();
        if (enemy == null)
        {
            Debug.LogError("Can't find Enemy");
            yield break;
        }
        //暂停敌人的移动
        enemy.EnterSkillCastMode();
        float time = 0;
        float _deltaTime = 1f / 60.0f;
        do
        {
            if (enemy == null) yield break; // Check if enemy is still valid
            //如果指示期间敌人被控制，则退出技能释放
            if (!enemy.IsUnderControl) 
            {
                time += _deltaTime;
                yield return new WaitForSeconds(_deltaTime);
            }
            else
            {
                enemy.ExitSkillCastMode();
                yield break; // Exit the skill casting mode and stop the coroutine
            }
        }
        while (time < _loopTime / 2f);     

        float _range = skillData.attackDistance;
        string _enemyName = deployer.owner.GetComponent<CharacterStats>().characterData.charName;
        
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        for (int i = 0; i < 3; i++)
        {
            Vector3 randomPos;
            int attempts = 0;
            do
            {
                randomPos = player.transform.position + new Vector3(UnityEngine.Random.Range(-_range, _range), UnityEngine.Random.Range(-_range, _range), 0f);
                attempts++;
            } while (!LocationUtils.isVaildBuildPosition(randomPos) && attempts < 10);

            if (randomPos != null)
            {
                GameObject _boss = Instantiate(Resources.Load("Prefabs/Boss/" + _enemyName) as GameObject, randomPos, Quaternion.identity);
                Enemy _enemy = _boss.GetComponent<Enemy>();
                //分身的血量为本体血量的20%,至少为1滴血
                _enemy.maxHealth = Math.Max((int)(0.2f), 1);
                //分身无法使用技能
                DisableSkills();
                //设置寻路ai
                _enemy.GetComponent<AIDestinationSetter>().target = player.transform;
            }
        }
        _usingSkill = false;
        
    }
}
