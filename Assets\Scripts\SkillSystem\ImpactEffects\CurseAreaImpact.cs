using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CurseAreaImpact : IImpactEffect
{
    public IEnumerator Execute(SkillDeployer deployer)
    {
        //创建一个skillData的复制，防止协程执行时数据被修改
        SkillData skillData = ScriptableObject.CreateInstance<SkillData>();
        skillData = deployer.SkillData;
        float attactDamage = skillData.coefficient;
        //指定刷新时间
        float _deltaTime = 1f; // 1秒
        //添加爆炸buff
        do
        {
            //查询范围内指定的目标
            List<string> attackTargetTags = skillData.attackTargetTags;
            //获取所有目标的mask
            LayerMask targetMask = LayerMask.GetMask(attackTargetTags.ToArray());
            //查询范围内指定图层的目标 
            Collider[] colliders = Physics.OverlapSphere(deployer.owner.transform.position,
                                     skillData.attackDistance,
                                     targetMask);
            //对目标造成基于攻击力的伤害
            for (int i = 0; i < colliders.Length; i++)
            {
                //获取目标
                GameObject target = colliders[i].gameObject;
                //获取AttributeProperty
                AttributeProperty attributeProperty = target.GetComponent<AttributeProperty>();
                if (attributeProperty != null)
                {
                    //获取buffManager
                    BuffManager buffManager = target.GetComponent<BuffManager>();
                    if (buffManager != null)
                    {
                        attributeProperty.ChangeHealth(-(int)attactDamage);
                    }
                }
                yield return new WaitForSeconds(_deltaTime);
            }
        }
        while (true);
    }
}

