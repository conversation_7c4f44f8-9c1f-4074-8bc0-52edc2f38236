[api-updater (non-obsolete-error-filter)] 2024/6/26 22:01:26 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 908.244ms
moved types parse time: 37ms
candidates parse time : 0ms
C# parse time         : 565ms
candidates check time : 19ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/9/19 21:45:05 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 232.6485ms
moved types parse time: 41ms
candidates parse time : 1ms
C# parse time         : 225ms
candidates check time : 28ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/9/19 21:49:05 : Starting H:/Works/2020.3.48f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 63.5195ms
moved types parse time: 36ms
candidates parse time : 1ms
C# parse time         : 122ms
candidates check time : 29ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/10/21 21:34:01 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 254.006ms
moved types parse time: 42ms
candidates parse time : 1ms
C# parse time         : 141ms
candidates check time : 21ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/10/21 22:58:09 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 175.1978ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 148ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:02:27 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 201.1059ms
moved types parse time: 36ms
candidates parse time : 0ms
C# parse time         : 194ms
candidates check time : 28ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:03:15 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 70.4641ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 119ms
candidates check time : 32ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:03:27 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 67.5819ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 123ms
candidates check time : 31ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:03:42 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 71.1445ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 120ms
candidates check time : 26ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:03:53 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 77.9481ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 118ms
candidates check time : 25ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:06:44 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 103.0104ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 133ms
candidates check time : 28ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:07:56 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 70.9829ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 124ms
candidates check time : 42ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:12:34 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 62.244ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 121ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:12:57 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 64.8241ms
moved types parse time: 32ms
candidates parse time : 0ms
C# parse time         : 121ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:14:21 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 55.4341ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 120ms
candidates check time : 22ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:15:36 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 67.9077ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 118ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:15:45 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 55.7515ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 124ms
candidates check time : 26ms
console write time    : 1ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:18:03 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 67.7143ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 120ms
candidates check time : 22ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:18:35 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 68.7573ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 121ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:18:53 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 49.567ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 120ms
candidates check time : 22ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:19:07 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 59.0878ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 120ms
candidates check time : 18ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:20:55 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 63.19ms
moved types parse time: 33ms
candidates parse time : 0ms
C# parse time         : 119ms
candidates check time : 17ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:21:08 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 68.3042ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 119ms
candidates check time : 20ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/11/18 23:21:23 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 52.1271ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 125ms
candidates check time : 20ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2024/12/4 22:18:57 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 113.8817ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 136ms
candidates check time : 18ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/1/8 23:25:57 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 193.2797ms
moved types parse time: 37ms
candidates parse time : 0ms
C# parse time         : 270ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/1/8 23:26:44 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 60.3669ms
moved types parse time: 34ms
candidates parse time : 0ms
C# parse time         : 148ms
candidates check time : 24ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/1/8 23:30:33 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 58.8217ms
moved types parse time: 35ms
candidates parse time : 0ms
C# parse time         : 145ms
candidates check time : 23ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/3/11 21:51:42 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 227.0383ms
moved types parse time: 38ms
candidates parse time : 0ms
C# parse time         : 230ms
candidates check time : 24ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/3/20 22:19:13 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 115.143ms
moved types parse time: 49ms
candidates parse time : 2ms
C# parse time         : 182ms
candidates check time : 24ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/3/31 22:13:47 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 144.8362ms
moved types parse time: 38ms
candidates parse time : 0ms
C# parse time         : 164ms
candidates check time : 36ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/4/8 21:31:03 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 131.0994ms
moved types parse time: 38ms
candidates parse time : 0ms
C# parse time         : 122ms
candidates check time : 17ms
console write time    : 0ms

[api-updater (non-obsolete-error-filter)] 2025/8/12 21:29:55 : Starting H:/Works/2020.3.42f1/Editor/Data/Tools/ScriptUpdater/APIUpdater.NonObsoleteApiUpdaterDetector.exe
[api-updater (non-obsolete-error-filter)] 
----------------------------------
jit/startup time      : 219.7292ms
moved types parse time: 41ms
candidates parse time : 0ms
C# parse time         : 233ms
candidates check time : 25ms
console write time    : 0ms

